package com.geeksec.certificateanalyzer.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 证书标签枚举
 * 标签ID范围：1001-1999
 * 
 * <AUTHOR>
 * @since 2025/06/20
 */
public enum CertificateLabel {
    // 证书信任相关标签
    TRUSTED_CA_CERT(1001, "Trusted CA Cert"),
    FAKE_CERT(1002, "Fake Cert"),
    CHAIN_MESS(1003, "Chain Mess"),
    SELF_SIGNED_CERT(1004, "Self Signed Cert"),
    UNKNOWN_CA(1005, "Unknown CA"),
    LONG_CERT_LIST(1006, "Long CertList"),
    SYSTEM_CERT(1007, "System Cert"),

    // 操作系统信任标签
    WINDOWS_TRUST(1008, "Windows Trust"),
    CENTOS_TRUST(1009, "Centos Trust"),
    APPLE_TRUST(1010, "Apple Trust"),
    ANDROID_TRUST(1011, "Android Trust"),
    FIREFOX_TRUST(1012, "Firefox Trust"),

    // 证书费用类型标签
    FREE_CERTIFICATE(1015, "Free Certificate"),
    PAID_CERTIFICATE(1016, "Paid Certificate"),

    // 证书类型标签
    ROOT_CA(1020, "Root CA"),
    INTERMEDIATE_CA(1021, "Intermediate CA"),
    END_ENTITY(1022, "End Entity"),
    EV_CERT(1023, "EV Cert"),

    // 签名验证标签
    SIGNATURE_VERIFIED(1024, "Signature Verified"),

    // 威胁相关标签
    THREAT(1030, "Threat"),
    APT(1031, "APT"),
    MALWARE(1032, "Malware"),
    PHISHING(1033, "Phishing"),
    BOTNET(1034, "Botnet"),
    TOR(1035, "Tor"),
    BLOCKED(1036, "Blocked"),
    SELF_SIGNED(1037, "Self-Signed"),
    TYPOSQUATTING(1038, "Typosquatting"),

    // 算法安全标签
    WEAK_ALGORITHM(1040, "Weak Algorithm"),
    STRONG_ALGORITHM(1041, "Strong Algorithm"),
    DEPRECATED_ALGORITHM(1042, "Deprecated Algorithm"),
    INSECURE_PUBKEY(1043, "Insecure PubKey"),

    // 证书状态标签
    EXPIRED(1050, "Expired"),
    REVOKED(1051, "Revoked"),
    VALID(1052, "Valid"),

    // 证书版本和用途标签
    INSECURE_VERSION(1060, "Insecure Version"),
    SERVER_CERT_AS_CLIENT(1061, "Server Cert as Client"),
    
    // 证书注册和使用标签
    RECENTLY_REGISTERED(1062, "Recent Registered Cert"),
    UNHOT_TLD(1063, "Unhot TLD Cert"),

    // APT29 相关标签
    LOST_CERT_LIST(1064, "Lost CertList"),
    SPECIAL_KEY_ID(1065, "Special Key ID"),
    IP_IN_SAN(1066, "IP in SAN"),
    WILDCARD_IN_ISSUER(1067, "Wild card in Issuer"),
    LONG_VALIDITY_CERT(1068, "Long Validity Cert"),

    // 证书类型详细标签
    USER_CERT(1070, "User Cert"),
    LEAF_CERT(1071, "Leaf Cert"),
    APP_LEAF_CERT(1072, "APP Leaf Cert"),
    PRIVATE_CA(1073, "Private CA"),
    CA_CERT(1074, "CA"),
    DV_CERT(1075, "DV Cert"),
    OV_CERT(1076, "OV Cert"),

    // SAN 相关标签
    WILDCARD_IN_SAN(1080, "Wildcard in SAN"),
    EMAIL_IN_SAN(1081, "Email in SAN"),
    URI_IN_SAN(1082, "URI in SAN"),

    // 域名和网络相关标签
    HOT_DOMAIN(1090, "Hot Domain"),
    CDN_CERT(1091, "CDN Cert"),
    TRANCO_TOP_DOMAIN(1092, "Tranco Top Domain"),
    FAKE_HOT_DOMAIN(1093, "Fake Hot Domain"),

    // 特殊用途标签
    CODE_SIGNING_CERT(1100, "Code Signing Cert"),
    EMAIL_CERT(1101, "Email Cert"),
    SERVER_AUTH_CERT(1102, "Server Auth Cert"),
    CLIENT_AUTH_CERT(1103, "Client Auth Cert"),

    // 威胁检测标签 - Botnet
    BOTNET_DANABOT(1110, "Botnet DanaBot Cert"),
    BOTNET_STEALC(1111, "Botnet Stealc Cert"),
    BOTNET_QUAKBOT(1112, "Botnet Quakbot Cert"),

    // 威胁检测标签 - APT
    APT28_CERT(1120, "APT28"),
    APT29_CERT(1121, "APT29"),
    APT_PATCHWORK(1122, "Patchwork Cert"),

    // 威胁检测标签 - Tor
    TOR_V2_CERT(1130, "Tor V2 Cert"),
    TOR_V3_CERT(1131, "Tor V3 Cert"),
    NETWORK_PENETRATION_CERT(1132, "Network Penetration Cert"),

    // 威胁检测标签 - C&C 和恶意活动
    CC_CERT(1140, "C&C Cert"),
    REMOTE_CONTROL_CERT(1141, "Remote Control Cert"),
    MALICIOUS_DOMAIN_CERT(1142, "Malicious Domain Cert"),
    IOC_IP_CERT(1143, "IOC IP Cert"),
    DISTRIBUTED_SERVICES_CERT(1144, "Distributed Services Cert"),
    MINING_CERT(1145, "Mining Cert"),

    // 特殊签名和生成标签
    OPENSSL_SIGNED(1150, "OpenSSL Signed"),

    // 信任相关标签
    WHITE_CERT(1160, "WhiteCert"),
    WITHDRAW_CERT(1161, "Withdraw Cert"),
    MICROSOFT_SERVER_GATED_CRYPTO(1162, "Microsoft Server Gated Crypto"),
    KEY_AGREEMENT(1163, "Key Agreement"),

    // 有效期相关标签
    SHORT_VALIDITY_CERT(1170, "Short Validity Cert"),
    LONG_DURATION_CERT(1171, "Long Duration Cert"),

    // 其他特殊标签
    UNCOMMON_OID(1180, "Uncommon OID"),
    SPECIAL_EXTENSION(1181, "Special Extension");

    private static final Map<Integer, CertificateLabel> ID_MAP = new HashMap<>();
    
    static {
        for (CertificateLabel label : values()) {
            ID_MAP.put(label.id, label);
        }
    }

    private final int id;
    private final String displayName;

    CertificateLabel(int id, String displayName) {
        this.id = id;
        this.displayName = displayName;
    }

    public int getId() {
        return id;
    }

    public String getDisplayName() {
        return displayName;
    }

    public static CertificateLabel fromId(int id) {
        return ID_MAP.get(id);
    }
}
